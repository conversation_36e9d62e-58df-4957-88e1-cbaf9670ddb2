package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/agent_referral"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/affiliate"
)

func main() {
	// Initialize the application
	global.GVA_VP = initializer.Viper("config.yaml")
	global.GVA_LOG = initializer.Zap()
	global.GVA_DB = initializer.GormPgSql()

	if global.GVA_DB == nil {
		log.Fatal("Failed to initialize database connection")
	}

	fmt.Println("🧪 Testing Invite Friends Task Flow...")

	ctx := context.Background()

	// Test the complete flow
	if err := testInviteFriendsFlow(ctx); err != nil {
		log.Fatalf("❌ Test failed: %v", err)
	}

	fmt.Println("✅ All tests passed!")
}

func testInviteFriendsFlow(ctx context.Context) error {
	fmt.Println("\n📋 Step 1: Setting up test data...")

	// Create test users
	userA := createTestUser("User A - Inviter")
	userB := createTestUser("User B - Invited")

	fmt.Printf("   👤 User A (Inviter): %s\n", userA.ID)
	fmt.Printf("   👤 User B (Invited): %s\n", userB.ID)

	// Step 2: Create referral relationship
	fmt.Println("\n📋 Step 2: Creating referral relationship...")
	invitationRepo := &agent_referral.InvitationRepository{}

	if err := createReferralRelationship(ctx, invitationRepo, userA.ID, userB.ID); err != nil {
		return fmt.Errorf("failed to create referral relationship: %w", err)
	}
	fmt.Println("   ✅ Referral relationship created")

	// Step 3: Verify task exists and is properly configured
	fmt.Println("\n📋 Step 3: Verifying Invite Friends task configuration...")
	taskRepo := &activity_cashback.ActivityTaskRepository{}

	task, err := taskRepo.GetByTaskIdentifier(ctx, model.TaskIDInviteFriends)
	if err != nil {
		return fmt.Errorf("failed to get invite friends task: %w", err)
	}

	fmt.Printf("   📝 Task ID: %s\n", task.ID)
	fmt.Printf("   📝 Task Name: %s\n", task.Name)
	fmt.Printf("   📝 Points: %d\n", task.Points)
	fmt.Printf("   📝 Frequency: %s\n", task.Frequency)
	fmt.Printf("   📝 Is Active: %t\n", task.IsActive)

	if task.Points != 100 {
		return fmt.Errorf("expected 100 points, got %d", task.Points)
	}
	if task.Frequency != model.FrequencyUnlimited {
		return fmt.Errorf("expected unlimited frequency, got %s", task.Frequency)
	}

	// Step 4: Check initial state - no completions yet
	fmt.Println("\n📋 Step 4: Checking initial completion state...")
	factory := activity_cashback.NewTaskCompletionRepositoryFactory()
	completionRepo := factory.GetRepositoryByFrequency(model.FrequencyUnlimited).(activity_cashback.UnlimitedTaskCompletionRepositoryInterface)

	initialCompletions, err := completionRepo.GetByUserAndTask(ctx, userA.ID, task.ID, 100, 0)
	if err != nil {
		return fmt.Errorf("failed to get initial completions: %w", err)
	}
	fmt.Printf("   📊 Initial completions for User A: %d\n", len(initialCompletions))

	// Step 5: Simulate User B's first transaction
	fmt.Println("\n📋 Step 5: Simulating User B's first transaction...")

	// Create affiliate service
	affiliateService := affiliate.NewActivityCashbackService()

	// Create a mock transaction for User B
	affiliateTx := &model.AffiliateTransaction{
		UserID:    userB.ID,
		OrderID:   uuid.New(),
		VolumeUSD: decimal.NewFromFloat(100.0),
		Status:    model.StatusCompleted,
		CreatedAt: time.Now(),
	}

	// Process the transaction (this should trigger invite friends completion)
	if err := affiliateService.ProcessActivityTransactionCashback(ctx, affiliateTx); err != nil {
		return fmt.Errorf("failed to process transaction: %w", err)
	}
	fmt.Println("   ✅ Transaction processed")

	// Step 6: Verify User B's FirstTransactionAt is set
	fmt.Println("\n📋 Step 6: Verifying User B's FirstTransactionAt...")

	var updatedUserB model.User
	if err := global.GVA_DB.WithContext(ctx).First(&updatedUserB, "id = ?", userB.ID).Error; err != nil {
		return fmt.Errorf("failed to get updated User B: %w", err)
	}

	if updatedUserB.FirstTransactionAt == nil {
		return fmt.Errorf("User B's FirstTransactionAt should be set")
	}
	fmt.Printf("   ✅ User B's FirstTransactionAt: %s\n", updatedUserB.FirstTransactionAt.Format(time.RFC3339))

	// Step 7: Verify task completion for User A
	fmt.Println("\n📋 Step 7: Verifying task completion for User A...")

	// Wait a moment for async processing
	time.Sleep(2 * time.Second)

	finalCompletions, err := completionRepo.GetByUserAndTask(ctx, userA.ID, task.ID, 100, 0)
	if err != nil {
		return fmt.Errorf("failed to get final completions: %w", err)
	}

	fmt.Printf("   📊 Final completions for User A: %d\n", len(finalCompletions))

	if len(finalCompletions) != len(initialCompletions)+1 {
		return fmt.Errorf("expected %d completions, got %d", len(initialCompletions)+1, len(finalCompletions))
	}

	// Check the latest completion
	latestCompletion := finalCompletions[len(finalCompletions)-1]
	fmt.Printf("   ✅ Latest completion ID: %s\n", latestCompletion.ID)
	fmt.Printf("   ✅ Points awarded: %d\n", latestCompletion.PointsAwarded)
	fmt.Printf("   ✅ Completion date: %s\n", latestCompletion.CompletionDate.Format(time.RFC3339))

	if latestCompletion.PointsAwarded != 100 {
		return fmt.Errorf("expected 100 points awarded, got %d", latestCompletion.PointsAwarded)
	}

	// Step 8: Verify verification data
	fmt.Println("\n📋 Step 8: Verifying completion verification data...")

	if latestCompletion.VerificationData == nil {
		return fmt.Errorf("verification data should not be nil")
	}

	verificationData := *latestCompletion.VerificationData
	if verificationData.CustomData != nil {
		if invitedUserID, ok := verificationData.CustomData["invited_user_id"]; ok {
			if invitedUserID != userB.ID.String() {
				return fmt.Errorf("expected invited_user_id %s, got %s", userB.ID.String(), invitedUserID)
			}
			fmt.Printf("   ✅ Invited User ID in verification data: %s\n", invitedUserID)
		} else {
			return fmt.Errorf("invited_user_id not found in verification data")
		}
	} else {
		return fmt.Errorf("custom data not found in verification data")
	}

	fmt.Println("\n🎉 Invite Friends task flow verification completed successfully!")
	fmt.Println("✅ User A invited User B")
	fmt.Println("✅ User B registered and made first transaction")
	fmt.Println("✅ User A automatically received 100 points")
	fmt.Println("✅ Task completion was properly recorded with verification data")

	return nil
}

func createTestUser(name string) *model.User {
	email := fmt.Sprintf("<EMAIL>", uuid.New().String()[:8])
	invitationCode := fmt.Sprintf("INV_%s", uuid.New().String()[:8])

	user := &model.User{
		ID:             uuid.New(),
		Email:          &email,
		InvitationCode: &invitationCode,
		AgentLevelID:   1,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	if err := global.GVA_DB.Create(user).Error; err != nil {
		log.Fatalf("Failed to create test user %s: %v", name, err)
	}

	return user
}

func createReferralRelationship(ctx context.Context, repo *agent_referral.InvitationRepository, referrerID, userID uuid.UUID) error {
	// Create referral relationship
	referral := &model.Referral{
		UserID:     userID,
		ReferrerID: &referrerID,
		Depth:      1,
		CreatedAt:  time.Now(),
	}

	return global.GVA_DB.WithContext(ctx).Create(referral).Error
}
